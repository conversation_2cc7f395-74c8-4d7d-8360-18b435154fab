@import url("https://fonts.googleapis.com/css2?family=Alata&display=swap");


.forpc{
  display: block;
}
.forphone {
  display: none;
}
.mbr-parallax-background-fixed{
    position: relative;
    background-attachment: fixed;
    background-position: 50% top;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
/*    background-color: #000340;*/
    background-image: url(../img/exbg.jpg);
}

#footer {
    background: #000;
}
#footer .con {
    margin-bottom: .5rem;
}
#footer a.co-qa{
  color: #ff8b00;
}

/*展會*/
#main {
    padding-top: 10rem;
    padding-bottom: 4rem;
}
#main.flex {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
#main .summit {
    max-width:400px;
}
#main .main-sub{
    z-index: 5;
}
.main-title {
    font-size: 4.5rem;
    line-height: 1.25;
    font-weight: 700;
    letter-spacing: 2px;
}
.main-txt {
    margin-top: 4rem;
}
.main-txt h5{
    font-weight: 400;
}

#exhibition .flex{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    background-image: linear-gradient(-45deg, #48c7db 0%, #07044c 80%);
    padding: 30px 4.6%;
    border-radius: 20px 0px 20px 20px;
}
#exhibition .flex .box{
    flex-basis: 45%; 
}
#exhibition .flex .box2{
    flex-basis: 45%; 
}
#exhibition .flex2{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-end;
    align-items: center;
}
#exhibition .flex2 p{
    flex-basis: 75%; 
    margin: 0;
    margin-right: 1rem;
}
#exhibition .box-top{
    margin-top: 2rem;
}
#exhibition .deco_dot{
    position: relative;
    overflow: hidden;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    color: #000340;
    text-align: center;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.modal-header{
    color: #000; 
}
.modal-body{
    background-color: #f3f4f6; 
    color: #000; 
}
.modal-body p, .modal-body h5{
    margin: 0; 
}
.modal-body .info, .modal-body .pro, .modal-body .co{
    border-radius: 0.6rem;
    background-color: #fff;
    margin-bottom: 1rem;
}
.modal-body .title{
    font-size: .9rem;
    font-weight: 400;
    margin-bottom: .5rem;
}
.modal-body .pro a{
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
    color: #000;
}
.modal-body .pro .pic {
    padding-right: 10px;
}
.modal-body .co p{
    margin-bottom: .5rem;
    padding-bottom: .5rem;
    border-bottom: 1px solid #eee;
}
.modal-body .co .doland {
    padding: 0.2rem 0.8rem 0.4rem;
    margin: 0px;
    font-size: 1rem;
    border-radius: 5px;
    background: #e24e0f;
    color: #fff;
}
.modal-body .co .doland:hover {
    background: #00d8f3;
}
.modal .modal-header button{
    background: #e8e8e800;
    border: 0px;
}

/* ==================================================*/

@media (max-width: 992px) {
}

@media (max-width: 768px) {
    .mbr-parallax-background-fixed {
        background-image: url(../img/exbg-sm.jpg);
        background-position: -99999px -99999px;
    }
    .mbr-parallax-background-fixed:before {
        content: "";
        position: fixed;
        z-index: -1;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        height: 100vh;
        background-image: url(../img/exbg-sm.jpg);
        -webkit-background-size: cover;
         !important;
        -o-background-size: cover;
        background-size: cover;
         !important;
        background-position: 50% top;
    }
    #maincon .main-txt a {
        margin-top: 1rem;
    }
    #exhibition .flex .box{
        flex-basis: 100%; 
    }
    #exhibition .flex .box2{
        flex-basis: 100%; 
    }
    #exhibition .flex2 {
        justify-content: space-between;
    }
    #exhibition .deco_dot {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .forpc{
        display: none;
    }
    .forphone{
        display:block;
    }
    #exhibition .flex2 p br{
        display: none;
    }
}

@media (max-width: 414px) {
    #main .summit {
        max-width: 160px;
    }
    .main-sub h3 {
        font-size: 1.2rem;
    }
    .main-title {
        font-size: 3.5rem;
    }
    #exhibition .deco_dot {
        width: 30px;
        height: 30px;
        font-size: 15px;
    }
}

@media (max-width: 375px) {
}
.navbar {
    background-color: #000000c3;
}
nav .mainlogo {
    display: flex;
    align-items: center;
}
nav .mainlogo img {
    max-width: 180px;
    text-align: left;
    padding-right: 5px;
}
nav .mainlogo img.fm {
    max-width: 180px;
}
.navbar-expand-lg .navbar-collapse {
    flex-basis: 80%;
}
.btn-primary {
    background-color: #f3b641;
    color: #fff;
    border: solid 2px #f3b641;
}
.btn-primary:hover  {
    background-color: #e2810a;
    color: #fff;
    border: solid 2px #e2810a;
}
#footer .footer_bnextmedia li {
    padding: 0 0px;
    display: inline-block !important;
}
@media (max-width: 576px) {
    #footer .footer_bnextmedia li {
        margin: .2rem 0 0 .7rem;
    }

#footer .footer_bnextmedia li {
    padding: 0 5px;
    display: inline-block !important;
}
.mainlogo img {
    width: 70px;
    height: auto;
}
}
.navbar .btn {
    padding: 0.2rem 0.8rem 0.4rem;
    margin: 0px;
    color: #000;
    font-weight: bold;
    font-size: 1rem;
    margin: 0 0.2rem;
}
