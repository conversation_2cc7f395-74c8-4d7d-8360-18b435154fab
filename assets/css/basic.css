/* Basic CSS - Header/Navbar/Footer Styles */

/* CSS Variables */
:root {
    --main-color: #1d2b84;
    --sub-color: #011B3B;
    --sub2-color: #11c5c9;
    --sub3-color: #e75c21;
    --white-color: #fff;
    --black-color: #1e1e1e;
}

/* Basic Font Settings */
* {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, 'Noto Sans TC', "微軟正黑體", sans-serif;
    color: var(--white-color);
}

/* Responsive Display Classes */
.forpc {
    display: block;
}

.forphone {
    display: none;
}

@media (max-width: 576px) {
    .forpc {
        display: none;
    }
    .forphone {
        display: block;
    }
}

/* ========== NAVBAR STYLES ========== */
.navbar {
  margin: auto;
  width: 100%;
    background-color: #080a14e6;
  padding-left: 30px;
  padding-right: 30px;
}
.navbar-fixed-top {
  position: fixed;
  top: 0;
  z-index: 15;
}
.mainlogo {
  display: flex;
  align-items: center;
}
.mainlogo img {
  max-width: 140px;
  text-align: left;
  padding-right: 5px;
}
.mainlogo img.school {
  max-width: 260px;
}
.navbar-expand-lg .navbar-collapse {
  flex-basis: 100%;
}

.navbar-nav {
    display: flex;
    flex-direction: row;
    text-align: center;
    justify-content: center;
    align-items: center;
    margin: 0;
}

.nav-item {
    flex-wrap: wrap;
    position: relative;
    margin-left: 12px;
}

.nav-item a {
    color: #ffffffa4;
    font-size: 1rem;
    line-height: 1rem;
    letter-spacing: 1px;
}

.nav-item a:hover {
    color: var(--sub3-color);
    position: relative;
}

.nav-item.active a {
    color: var(--white-color);
    font-weight: 500;
}

.nav-item.active::before {
    content: '';
    border: 1px solid var(--sub3-color);
    font-size: 10px;
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
}

/* Dropdown Menu */
.navbar-nav .dropdown-menu li {
    padding: 0;
    margin: 0;
}

.navbar-nav .dropdown-menu li a {
    color: #666666;
    text-shadow: none;
    padding: 8px 16px;
}

.navbar-nav .dropdown-menu li a:hover {
    color: var(--sub3-color);
    background-color: transparent !important;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

a.dropdown-item:focus,
a.dropdown-item:hover {
    text-decoration: none;
    background-color: #00d8f3;
}

/* Navbar Brand/Logo */
.navbar-brand .mainlogo {
    max-height: 45px;
}

nav .nav-link {
    margin-top: 0;
    margin-bottom: 0;
}

nav .mainlogo {
    display: flex;
    align-items: center;
}

nav .mainlogo img {
    max-width: 140px;
    text-align: left;
    padding-right: 5px;
}

nav .mainlogo img.fm {
    max-width: 120px;
}

/* Navbar Buttons */
.nav-item a.btn-ticket,
.btn-ticket {
    background-color: var(--sub3-color);
    color: var(--white-color);
    border: solid 2px var(--sub3-color);
    border-radius: 2rem;
    padding: 0.75rem 1rem !important;
}

.nav-item a.btn-ticket:hover,
.nav-item a.btn-ticket:focus,
.btn-ticket:hover,
nav .btn-ticket:focus {
    background-color: #f39441;
    color: var(--black-color);
    border: solid 2px #f39441;
}

.navbar .btn {
    padding: 0.2rem 0.8rem 0.4rem;
    margin: 0px;
    color: #000;
    font-weight: bold;
    font-size: 1rem;
    margin: 0 0.2rem;
}

.btn-primary {
    background-color: #f3b641;
    color: #fff;
    border: solid 2px #f3b641;
}

.btn-primary:hover {
    background-color: #e2810a;
    color: #fff;
    border: solid 2px #e2810a;
}

/* Navbar Toggle Animation */
.navbar-toggler-icon {
    background-image: none !important;
    background-color: var(--bs-gray-800);
    height: 3px;
    width: 25px;
    margin: 10px 0;
    position: relative;
    transition: all 0.35s ease-out;
    transform-origin: center;
}

.navbar-toggler-icon::before {
    display: block;
    background-color: var(--bs-gray-800);
    height: 3px;
    content: "";
    position: relative;
    top: -7px;
    transition: all 0.15s ease-out;
    transform-origin: center;
}

.navbar-toggler-icon::after {
    display: block;
    background-color: var(--bs-gray-800);
    height: 3px;
    content: "";
    position: relative;
    top: 4px;
    transition: all 0.35s ease-out;
    transform-origin: center;
}

.navbar-dark .navbar-toggler-icon,
.navbar-dark .navbar-toggler-icon::before,
.navbar-dark .navbar-toggler-icon::after {
    background-color: var(--bs-gray-100);
}

.navbar-toggler:not(.collapsed) .navbar-toggler-icon {
    transform: rotate(45deg);
}

.navbar-toggler:not(.collapsed) .navbar-toggler-icon::before {
    opacity: 0;
}

.navbar-toggler:not(.collapsed) .navbar-toggler-icon::after {
    transform: rotate(-90deg) translateX(7px);
}

.navbar-expand-lg .navbar-collapse {
    flex-basis: 80%;
}

/* Mobile Navbar Styles */
@media (max-width: 768px) {
    nav .navbar-collapse {
        display: flex;
    }

    nav .navbar-collapse .navbar-nav {
        width: 100%;
    }

    .nav-item a {
        font-size: 1rem;
        line-height: 1rem;
        padding: 0.75rem 0;
    }

    .navbar-brand {
        margin: 0;
    }

    .navbar-toggler,
    .navbar-toggler:focus,
    .navbar-toggler:active {
        padding: 2px;
        border: 0px;
    }

    .navbar img {
        width: 100px;
    }

    nav .mainlogo img.fm {
        max-width: 90px;
    }

    nav .mainlogo img.school {
        max-width: 120px;
    }

    nav .mainlogo img {
        max-width: 90px;
        height: auto;
    }

    .navbar {
        margin: auto;
        width: 100%;
        background: rgb(0 0 0);
        padding-left: 12px;
        padding-right: 12px;
    }

    .mainlogo img {
        width: 70px;
        height: auto;
    }
}

@media (max-width: 992px) {
    .navbar-nav {
        flex-direction: column;
    }

    .nav-item {
        width: 100%;
        text-align: left;
    }

    .dropdown-menu {
        background-color: #ffffffd1;
    }
}

/* ========== FOOTER STYLES ========== */
#footer {
    background: linear-gradient(-45deg, #0c272a 10%, #04090bdb 100%);
    position: relative;
}

#footer .con {
    margin-bottom: 1rem;
}

#footer a.co-qa {
    color: #ff8b00;
}

#footer .footer_bnextmedia li {
    padding: 0 0px;
    display: inline-block !important;
}

#footer .display-4 {
    color: #fff;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 300;
}

#footer .h6 {
    font-size: 1.12rem;
    line-height: 1.5;
    font-weight: 300;
}

#footer a {
    color: var(--sub2-color);
    text-decoration: none;
    transition: 0.5s;
}

#footer a:hover {
    text-decoration: none !important;
}

#footer .a_white {
    color: #fff;
}

@media (max-width: 576px) {
    #footer .footer_bnextmedia li {
        margin: .2rem 0 0 .7rem;
        padding: 0 5px;
        display: inline-block !important;
    }
}

/* ========== MOBILE AD BUTTON ========== */
.adbtn {
    background-color: var(--sub2-color);
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    padding: 0.75rem;
    border-radius: 1rem 1rem 0 0;
    box-shadow: 0 -6px 2rem #2828287d;
}

.adbtn a {
    font-size: 18px;
    letter-spacing: 2px;
    color: var(--white-color);
}

.adbtn:hover {
    background-color: var(--main-color);
}

/* ========== GENERAL LINK STYLES ========== */
a {
    color: var(--sub2-color);
    text-decoration: none;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}

a:hover {
    text-decoration: none !important;
}