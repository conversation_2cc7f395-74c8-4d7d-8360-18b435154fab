<svg width="1287" height="146" viewBox="0 0 1287 146" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_54_49)">
<g filter="url(#filter0_d_54_49)">
<path d="M0.168761 98.256C0.168761 92.231 2.49152 87.1624 7.14697 83.0504C11.7925 78.9383 18.8303 75.0531 28.2504 71.3947C37.2635 68.1405 42.6634 65.7936 44.4402 64.3441C46.217 62.8945 47.1104 60.9519 47.1104 58.5064C47.1104 55.1339 46.3858 52.9645 44.9266 51.9981C43.4674 51.0317 41.3134 50.5485 38.4447 50.5485C35.0102 50.5485 32.3003 51.1501 30.315 52.3531C28.3298 53.5561 27.3471 56.3862 27.3471 60.8434H0C0 53.8125 2.45183 47.255 7.34551 41.151C12.2392 35.047 22.6023 32 38.4546 32C54.307 32 64.8686 34.909 68.8292 40.7368C72.7898 46.5548 74.7652 52.3432 74.7652 58.0823C74.7652 64.9061 72.234 70.4677 67.1814 74.7671C62.1289 79.0665 55.6967 83.0011 47.8946 86.5707C41.4722 89.105 36.7275 90.9589 33.6801 92.1422C30.6228 93.3255 29.104 94.4398 29.104 95.4851H74.6361V114.034H0.178697V98.256H0.168761Z" fill="white"/>
</g>
<g filter="url(#filter1_d_54_49)">
<path d="M100.603 109.005C94.5581 105.011 91.5405 98.6505 91.5405 89.9432V57.0568C91.5405 48.3397 94.5879 41.9892 100.673 37.9955C106.758 34.0018 116.019 32 128.476 32C140.934 32 150.185 34.0018 156.24 37.9955C162.305 41.9892 165.333 48.3495 165.333 57.0568V89.9432C165.333 98.6603 162.295 105.011 156.23 109.005C150.165 112.998 140.914 115 128.457 115C115.999 115 106.638 112.998 100.603 109.005ZM135.564 94.637C137.023 93.434 137.748 91.4815 137.748 88.7993V58.1415C137.748 55.4494 137.033 53.5167 135.604 52.3334C134.164 51.1501 131.792 50.5584 128.476 50.5584C125.002 50.5584 122.56 51.1402 121.171 52.3038C119.771 53.4674 119.076 55.4199 119.076 58.1415V88.7993C119.076 91.5308 119.761 93.4833 121.161 94.6765C122.55 95.8598 124.992 96.4515 128.467 96.4515C131.742 96.4515 134.105 95.8499 135.564 94.6469V94.637Z" fill="white"/>
</g>
<g filter="url(#filter2_d_54_49)">
<path d="M183.21 98.256C183.21 92.231 185.533 87.1624 190.188 83.0504C194.834 78.9383 201.872 75.0531 211.292 71.3947C220.305 68.1405 225.705 65.7936 227.482 64.3441C229.259 62.8945 230.152 60.9519 230.152 58.5064C230.152 55.1339 229.427 52.9645 227.968 51.9981C226.509 51.0317 224.355 50.5485 221.486 50.5485C218.052 50.5485 215.342 51.1501 213.357 52.3531C211.371 53.5561 210.389 56.3862 210.389 60.8434H183.042C183.042 53.8125 185.493 47.255 190.377 41.151C195.271 35.047 205.634 32 221.486 32C237.339 32 247.9 34.909 251.861 40.7368C255.821 46.5548 257.797 52.3432 257.797 58.0823C257.797 64.9061 255.266 70.4677 250.213 74.7671C245.161 79.0665 238.728 83.0011 230.926 86.5707C224.494 89.105 219.759 90.9589 216.712 92.1422C213.654 93.3255 212.136 94.4398 212.136 95.4851H257.668V114.034H183.21V98.256Z" fill="white"/>
</g>
<g filter="url(#filter3_d_54_49)">
<path d="M282.126 106.687C277.054 101.224 274.523 95.0019 274.523 88.0105H301.87C301.989 92.3887 302.902 94.9625 304.599 95.7513C306.297 96.5304 308.64 96.9248 311.637 96.9248C314.635 96.9248 317.057 96.3726 318.675 95.2681C320.293 94.1637 321.097 90.9589 321.107 85.6635C321.107 81.2064 320.293 78.4453 318.655 77.3803C317.017 76.3153 314.685 75.7828 311.657 75.7828C308.63 75.7828 306.396 76.1871 304.719 76.9859C303.041 77.7846 302.098 78.9383 301.899 80.4175H274.552V32.7198H346.647V51.15H301.899V62.9537C308.242 60.5476 315.032 59.3347 322.268 59.3347C332.085 59.3347 339.004 61.7309 343.004 66.5332C347.005 71.3355 349 77.5479 349 85.1705C349 93.8876 346.449 101.007 341.357 106.549C336.264 112.091 325.891 114.862 310.248 114.862C296.589 114.862 287.218 112.13 282.146 106.667L282.126 106.687Z" fill="white"/>
</g>
<g filter="url(#filter4_d_54_49)">
<path d="M397.536 31.2017H437.269V15.9978H456.67V31.2017H496.879V49.2533H456.66V61.6095H503.492V79.661H463.748C473.206 90.4342 486.926 98.987 504.899 105.319C503.63 107.221 501.896 110.069 499.695 113.872C497.167 117.676 495.273 120.683 494.014 122.903C477.3 115.625 464.839 106.753 456.65 96.2987V129.086H437.249V96.7766C429.051 107.231 416.748 116.252 400.351 123.859C398.765 121.639 396.247 117.994 392.787 112.926C391.211 110.397 389.952 108.496 389 107.221C407.608 99.6143 421.487 90.4342 430.637 79.661H390.903V61.6095H437.269V49.2533H397.536V31.2017Z" fill="white"/>
</g>
<g filter="url(#filter5_d_54_49)">
<path d="M567.464 15.5199H586.865V26.9204H631.803V44.026H586.855V76.8134C597.254 90.4342 613.334 100.261 635.094 106.275C631.307 112.618 628.155 118.313 625.627 123.381C607.654 116.093 594.716 108.336 586.845 100.102V129.564H567.444V100.102C559.88 108.346 547.26 116.411 529.604 124.337C528.97 123.072 527.869 121.171 526.293 118.632C523.458 114.201 521.247 110.557 519.671 107.699C540.489 101.048 556.41 90.7528 567.454 76.8134V44.026H522.992V26.9204H567.464V15.5199ZM521.088 78.7152C530.229 69.8537 535.909 59.2398 538.12 46.8836L555.627 47.8294C554.675 51.6329 553.565 55.4264 552.316 59.2299C557.669 63.3519 562.408 67.6234 566.502 72.0541L555.617 85.3563C554.348 84.0918 552.613 82.1901 550.413 79.6511C548.202 77.1221 546.467 75.2203 545.208 73.9459C542.055 79.6511 538.11 85.3463 533.381 91.0515C532.439 90.1056 531.012 88.6719 529.128 86.7701C527.869 85.187 525.183 82.4987 521.088 78.6952V78.7152ZM587.787 71.5862C595.043 63.9792 599.614 55.755 601.507 46.8836L619.014 47.8294C618.063 51.6329 616.804 55.2771 615.227 58.7619C621.215 63.8299 627.054 69.0571 632.725 74.4437L621.84 87.7459C617.111 82.9965 612.382 78.0879 607.653 73.0199C605.126 76.8234 602.449 80.3082 599.614 83.4745C595.827 79.3623 591.881 75.3996 587.787 71.5961V71.5862Z" fill="white"/>
</g>
<g filter="url(#filter6_d_54_49)">
<path d="M663.1 97.2446C662.783 101.994 662.148 108.964 661.206 118.154C660.889 121.639 660.572 124.168 660.255 125.751L648.904 124.327C649.855 119.886 650.956 113.245 652.215 104.374C652.522 100.889 652.849 98.2004 653.166 96.2987L663.1 97.2446ZM697.639 82.0407H761.978V99.1463H737.372V110.079H764.337V126.229H693.376V120.055L688.171 121.479C687.537 118.94 686.595 114.828 685.336 109.123C684.077 103.109 683.125 98.8377 682.501 96.2987L691.958 93.9191C692.266 94.8649 692.583 96.1394 692.9 97.7225C694.476 102.79 695.577 106.913 696.211 110.079H720.341V99.1463H697.639V90.1156L685.336 93.4411C685.019 91.858 684.543 90.1156 683.918 88.2139H680.131V130.5H665.459V89.1598H664.052C663.417 89.1598 662.317 89.3191 660.74 89.6377C657.588 89.6377 655.377 89.9563 654.118 90.5836L649.389 76.3256H656.012C659.481 71.8948 663.586 66.0403 668.314 58.742L647.506 44.016L656.497 33.0836C657.439 33.4022 658.391 34.0295 659.333 34.9853C662.168 30.5546 665.489 24.0528 669.266 15.5L685.822 20.7273C680.459 28.3342 675.413 35.6225 670.674 42.5823L676.354 46.3857C676.979 45.1212 678.089 43.3788 679.665 41.1585C681.866 37.6736 683.452 34.9853 684.394 33.0836L698.115 41.1585C696.528 43.3788 694.01 47.023 690.541 52.0909C685.812 58.742 682.174 63.6606 679.656 66.8169L689.113 63.9693C690.689 68.0914 692.732 73.7965 695.259 81.0749C696.201 84.2411 696.984 86.6208 697.629 88.2039V82.0307L697.639 82.0407ZM680.141 74.9117C679.199 73.01 678.555 71.1082 678.248 69.2065L673.519 75.3797L680.141 74.9017V74.9117ZM708.058 59.2299L718.933 74.4338L705.213 81.5628C703.002 77.7593 699.542 72.2234 694.803 64.9351C693.227 62.4061 691.968 60.5043 691.016 59.2299C696.38 52.5788 701.267 45.6091 705.689 38.3208L718.933 44.494C716.722 47.9788 713.57 52.4195 709.466 57.7961C708.831 58.4334 708.355 58.9113 708.048 59.2199L708.058 59.2299ZM695.765 21.2152H762.939V38.3208H695.755V21.2152H695.765ZM730.294 59.2299C731.553 60.4944 733.437 62.874 735.965 66.3589C738.483 69.8437 740.376 72.3827 741.635 73.9559L728.391 81.5628C723.662 73.9559 718.765 66.5182 713.728 59.2299C719.092 52.5788 723.979 45.6091 728.4 38.3208L741.169 44.9719C739.583 47.5108 736.44 51.623 731.702 57.3282C731.067 58.274 730.591 58.9212 730.284 59.2299H730.294ZM752.53 59.2299C756.317 63.6706 760.094 68.2606 763.881 73.01L751.578 81.5628C746.85 73.9559 741.645 66.5182 735.974 59.2299C741.338 52.5788 746.384 45.6091 751.112 38.3208L763.415 44.9719C759.628 50.6771 756 55.4264 752.53 59.2299Z" fill="white"/>
</g>
<g filter="url(#filter7_d_54_49)">
<path d="M820.735 82.0407V19.3234H890.765V82.0407H864.256V88.2139H891.221V103.896H864.256V110.069H895.474V125.751H815.045V115.774C804.001 117.676 793.285 119.727 782.876 121.947L780.04 103.896C780.982 103.896 782.241 103.736 783.827 103.418C787.297 103.109 789.815 102.79 791.401 102.472V77.7693H782.895V60.6637H791.411V38.3308H782.429V20.7472H817.91V38.3308H808.918V60.6637H817.434V77.7693H808.918V100.102C811.754 99.4749 814.906 98.997 818.376 98.6784C818.059 102.163 817.9 105.967 817.9 110.079H846.759V103.906H820.745V88.2238H846.768V82.0507H820.755L820.735 82.0407ZM838.243 57.8061V66.3589H846.759V57.8061H838.243ZM838.243 35.0052V43.558H846.759V35.0052H838.243ZM873.248 66.3589V57.8061H864.266V66.3589H873.248ZM873.248 35.0052H864.256V43.558H873.238V35.0052H873.248Z" fill="white"/>
</g>
<g filter="url(#filter8_d_54_49)">
<path d="M956.611 16.4758H977.429C977.112 22.4996 976.795 28.3541 976.478 34.0593C977.419 70.8095 993.806 95.8307 1025.66 109.133C1024.07 111.035 1021.71 113.723 1018.56 117.208C1015.08 121.957 1012.41 125.442 1010.52 127.662C988.443 115.625 974.257 98.997 967.952 77.7693C962.896 97.4139 947.917 114.519 923.004 129.086C917.958 121.161 913.705 115.306 910.235 111.503C940.511 96.2987 955.659 73.6572 955.659 43.558C956.294 35.9511 956.611 26.9303 956.611 16.4758Z" fill="white"/>
</g>
<g filter="url(#filter9_d_54_49)">
<path d="M1147.81 58.284V74.9117H1116.59V90.5935H1154.9V108.177H1116.58V129.554H1097.18V108.177H1042.3V90.5935H1057.92V63.9892C1055.72 65.8909 1053.35 68.2606 1050.83 71.1182C1049.89 69.5351 1048.78 67.3147 1047.52 64.4671C1044.68 59.0805 1042.47 55.1277 1040.9 52.5887C1052.56 43.09 1061.56 30.7338 1067.87 15.5299L1090.11 17.4316C1088.21 20.5978 1086.32 23.9234 1084.43 27.4082H1151.6V44.5139H1116.59V58.294H1147.8L1147.81 58.284ZM1074.02 44.5039C1070.86 48.9446 1067.23 53.5346 1063.13 58.284H1097.2V44.5039H1074.02ZM1076.38 74.9117V90.5935H1097.19V74.9117H1076.38Z" fill="white"/>
</g>
<g filter="url(#filter10_d_54_49)">
<path d="M1184.33 129.554V88.6918H1272.33V129.554H1254.34V126.229H1202.31V129.554H1184.34H1184.33ZM1245.37 15.5199L1241.59 18.8455C1254.2 26.4524 1269.33 32.1476 1287 35.9511C1286.37 38.1714 1285.27 41.497 1283.69 45.9277C1282.74 49.0939 1282.1 51.3143 1281.8 52.5788C1279.9 51.9515 1278.32 51.4736 1277.07 51.155V84.8883H1179.61V52.5788C1178.98 52.5788 1178.19 52.7381 1177.25 53.0567C1175.98 53.3753 1175.04 53.6939 1174.41 54.0026C1173.78 50.8364 1172.05 45.4498 1169.21 37.8528C1192.54 31.839 1209.58 24.3914 1220.3 15.5199H1245.37ZM1202.31 111.971V115.774H1254.34V111.971H1202.31ZM1202.31 99.6143V103.418H1254.34V99.6143H1202.31ZM1196.64 60.6537V73.4779H1207.05C1206.41 72.8507 1204.99 71.5762 1202.79 69.6745C1200.58 67.7727 1199.01 66.3489 1198.07 65.403L1205.16 60.6537H1196.65H1196.64ZM1250.1 45.4498H1206.58V42.1243C1201.22 44.3446 1195.23 46.7143 1188.61 49.2533H1271.4C1263.5 46.4056 1256.42 43.5481 1250.11 40.7004V45.4498H1250.1ZM1218.4 67.3048L1210.36 73.4779H1219.82V60.6537H1209.88C1211.78 62.2468 1214.61 64.4571 1218.4 67.3048ZM1240.17 35.9511C1238.91 35.0052 1237.16 33.7407 1234.96 32.1476C1232.44 30.5645 1230.54 29.4593 1229.28 28.8221C1226.13 31.0424 1222.34 33.4221 1217.93 35.9511H1240.17ZM1238.26 67.3048C1238.89 66.6775 1240 65.881 1241.58 64.9251C1243.78 63.0234 1245.51 61.5996 1246.78 60.6537H1236.85V73.4779H1245.36L1238.26 67.3048ZM1259.55 73.4779V60.6537H1251.03L1258.13 65.403C1257.5 66.0403 1256.08 67.3048 1253.87 69.2065C1251.66 71.1082 1250.08 72.532 1249.14 73.4779H1259.55Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_54_49" x="-12" y="24" width="98.7651" height="106.034" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter1_d_54_49" x="79.5405" y="24" width="97.7925" height="107" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter2_d_54_49" x="171.042" y="24" width="98.7551" height="106.034" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter3_d_54_49" x="262.523" y="24.7198" width="98.4773" height="106.142" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter4_d_54_49" x="377" y="7.99785" width="139.899" height="137.088" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter5_d_54_49" x="507.671" y="7.51993" width="139.424" height="138.044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter6_d_54_49" x="635.506" y="7.50002" width="140.831" height="139" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter7_d_54_49" x="768.04" y="11.3234" width="139.434" height="130.427" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter8_d_54_49" x="898.235" y="8.47578" width="139.424" height="136.61" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter9_d_54_49" x="1028.9" y="7.52988" width="138.006" height="138.024" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<filter id="filter10_d_54_49" x="1157.21" y="7.51993" width="141.793" height="138.034" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_54_49"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_54_49" result="shape"/>
</filter>
<clipPath id="clip0_54_49">
<rect width="1287" height="146" fill="white"/>
</clipPath>
</defs>
</svg>
