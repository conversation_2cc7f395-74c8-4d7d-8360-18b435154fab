
$(document).ready(function(){
  $('#slider-carousel.owl-carousel').owlCarousel({
    autoplay:true,
    loop:true,
    margin: 0,
    nav: false,
    dots: false,
    stagePadding: 0,
    responsive:{
        0:{
            items: 1
        },
        600:{
            items: 2
        },
        1000:{
            items:4
        }
    }
  });

});

$(document).ready(function(){
  $('#slider-speake.owl-carousel').owlCarousel({
    autoplay:true,
    loop:true,
    nav: true,
    responsive:{
        0:{   
          mouseDrag: true,
          loop: true,
          center: true,
          items:1.5
        },
        600:{
          mouseDrag: false,
          autoplay:false,
          items:2.5
        },
        1000:{
          mouseDrag: false,
          autoplay:false,
          items:4
        }
    }
});
    
    $('.owl-carousel-trend').owlCarousel({
        loop: true,
        margin: 0,
        stagePadding: 0,
        autoplay: true,
        autoplayTimeout: 6000,
        dots: false,
        nav: true,
        autoplayHoverPause: true,
        navText: ["<i class='fa fa-angle-left'></i>", "<i class='fa fa-angle-right'></i>"],
        responsive: {
            0: {
                items: 1,
            },
            992: {
                items: 1
            }
        }
    });
    
});

$(document).ready(function () {
    $('.dropdown').hover(function () {
        $(this).addClass('show');
        $(this).find('.dropdown-menu').addClass('show');
    }, function () {
        $(this).removeClass('show');
        $(this).find('.dropdown-menu').removeClass('show');
    });
});



$(document).ready(function () {
    var owl = $('#photoss-carousel.owl-carousel');
    owl.owlCarousel({
        loop: true,
        autoplay:true,
        autoplayTimeout: 3000,
        nav: false,
        dots: false,
        items: 1,
        responsive:{
            0:{
                items:1
            },
            992:{
                items:1
            }
        }
    });
});

$(document).ready(function () {
    var owl = $('#trend-carousel.owl-carousel');
    owl.owlCarousel({
        nav: false,
        dots: false,
        loop: true,
        autoplay: true,
        autoplayTimeout: 3000,
        responsive: {
            0: {
                items: 1,
            },
            576: {
                items: 1
            },
            992: {
                items: 1
            }
        }
    });
});

$(document).ready(function () {
    var owl = $('#spk-carousel.owl-carousel');
    owl.owlCarousel({
        stagePadding: 50,
        margin:10,
        nav: true,
        dots: false,
        loop: true,
        lazyLoad:true,
        autoplay: true,
        autoplayHoverPause:true,
        autoplayTimeout: 2000,
        responsive: {
            0: {
                items: 1,
            },
            576: {
                items: 3
            },
            992: {
                items: 4
            },
            1440: {
                items: 5
            }
        }
    });
});


 //Text Ring
 const text = "AI FOR ALL: THE ACTION MAP OF LEADING TEAMS "; //文字可修改
 const repeatCount = 2; //重複次數
 const totalChars = text.length * repeatCount; //計算文字字元
 const angleStep = 360 / totalChars; //計算字元間距
 let output = "";
 
 for (let i = 0; i < totalChars; i++) {
   const char = text[i % text.length];
   const extraSpacing = (i % text.length === 0 && i !== 0) ? '   ' : '';
   output += `<span class='text-ring-char' style='transform: rotateY(${i * angleStep}deg) translateZ(160px);'>${extraSpacing}${char}</span>`;
 }
 
 document.addEventListener("DOMContentLoaded", () => {
   const banner = document.querySelector(".text-ring-banner");
   if (banner) banner.innerHTML = output;
 });
 




        // 監聽所有 collapse 元素的事件
        document.addEventListener('DOMContentLoaded', function() {
            const collapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');
            
            collapseElements.forEach(function(collapseElement) {
                // 獲取目標 collapse 元素的 ID
                const targetId = collapseElement.getAttribute('data-bs-target');
                const targetElement = document.querySelector(targetId);
                
                // 監聽 collapse 的 show 和 hide 事件
                targetElement.addEventListener('show.bs.collapse', function() {
                    // 找到當前 collapse 按鈕中的 icon 並切換
                    const icon = collapseElement.querySelector('.bi');
                    icon.classList.remove('bi-caret-down-fill');
                    icon.classList.add('bi-caret-up-fill');
                });
                
                targetElement.addEventListener('hide.bs.collapse', function() {
                    // 找到當前 collapse 按鈕中的 icon 並切換回來
                    const icon = collapseElement.querySelector('.bi');
                    icon.classList.remove('bi-caret-up-fill');
                    icon.classList.add('bi-caret-down-fill');
                });
            });
        });
