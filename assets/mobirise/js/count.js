const second = 1000;
const minute = second * 60;
const hour = minute * 60;
const day = hour * 24;

const countDown = new Date('Oct 14, 2020 23:59:59').getTime();
const elements = {
  days: document.getElementById('days'),
  hours: document.getElementById('hours'),
  minutes: document.getElementById('minutes'),
  seconds: document.getElementById('seconds')
};

const timer = setInterval(function() {
  const now = new Date().getTime();
  const distance = countDown - now;

  if (distance < 0) {
    clearInterval(timer);
    return;
  }

  if (elements.days) elements.days.innerText = Math.floor(distance / day);
  if (elements.hours) elements.hours.innerText = Math.floor((distance % day) / hour);
  if (elements.minutes) elements.minutes.innerText = Math.floor((distance % hour) / minute);
  if (elements.seconds) elements.seconds.innerText = Math.floor((distance % minute) / second);
}, second);