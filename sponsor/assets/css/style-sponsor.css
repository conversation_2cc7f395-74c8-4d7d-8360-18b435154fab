@import url("https://fonts.googleapis.com/css2?family=Alata&display=swap");

:root {
    /*主色輔色 改色*/
    --main-color: #1d2b84;
    --sub-color: #011B3B;
    --sub2-color: #34c7ca;
    --sub3-color:#e75c21;


    /*勿動*/
    --white-color: #fff;
    --black-color: #1e1e1e;
}


* {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, 'Noto Sans TC', "微軟正黑體", sans-serif;
}

.mbr-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
}
.mbr-section-hidden {
    overflow: hidden;
}
.date-en {
   font-family: 'Candal', 'Oswald',sans-serif;
}

a {
  color: #ff8b00;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
a:hover {
  text-decoration: none !important;
}
.btn-primary {
  background-color: #ff2871;
  color: #fff;
  border: solid 2px #ff2871;
}
.btn-primary:hover,.btn-primary:focus {
  background-color: #d0054a;
  color: #fff;
  border: solid 2px #d0054a;
}
.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  background-color: #d0054a;
  color: #fff;
  border: solid 2px #d0054a;
}
.card {
  position: relative;
  padding: 0;
  border: 0;
  border-radius: 0;
  margin: 0;
  overflow: hidden;
}

.navbar {
  margin: auto;
  width: 100%;
  background-color: rgba(23, 40, 59, 0.73);
  backdrop-filter: blur(10px);
}

.navbar-brand{
    margin-right: 0;
}
.navbar-fixed-top {
  position: fixed;
  top: 0;
  z-index: 15;
}
.mainlogo {
  display: flex;
  align-items: center;
}
.mainlogo img {
  max-width: 140px;
  text-align: left;
  padding-right: 5px;
}
.mainlogo img.school {
  max-width: 260px;
}
.navbar-expand-lg .navbar-collapse {
  flex-basis: 100%;
}
.nav-item {
  flex-wrap: wrap;
  position: relative;
  margin-left: 12px;
}

.nav-item a {
    color: var(--white-color);
    font-size: 1rem;
    line-height: 1rem;
    letter-spacing: 1px;
}

.nav-item a:hover {
    color: var(--sub3-color);
    position: relative;
}
.nav-item.active a{
    color: var(--white-color);
    font-weight: 500;
}
.nav-item.active::before {
    content: '';
    border: 1px solid var(--sub3-color);
    font-size: 10px;
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
}

.navbar-nav .dropdown-menu li{
    padding: 0;
    margin: 0;
}
.navbar-nav .dropdown-menu li a {
    color: #666666;
    text-shadow: none;
    padding: 8px 16px;
}

.navbar-nav .dropdown-menu li a:hover {
    color: var(--sub3-color);
    background-color: transparent !important;
}

.navbar-collapse {
  justify-content: flex-end;
}
.navbar-nav {
  display: flex;
  flex-direction: row;
  text-align: center;
  justify-content: center;
  align-items: center;
  margin: 0;
}
.navbar .btn{
  padding: 0.2rem 0.8rem 0.4rem;
  margin: 0px;
  color: #000;
  font-weight: bold;
  font-size: 1rem;
  margin: 0 0.2rem;
}
.dropdown:hover .dropdown-menu {
    display: block;
}
a.dropdown-item:focus, a.dropdown-item:hover {
    text-decoration: none;
    background-color: #34b9f7;
}
.dropdown-item.item-close {
    color: #ccc;
}

.nav-item a.btn-ticket, .btn-ticket {
    background-color: #ccff00;
    color: #000;
    border: solid 2px #ccff00;
    border-radius: 2rem;
    padding: 0.75rem 1rem !important;
}
.nav-item a.btn-ticket:hover, .nav-item a.btn-ticket:focus, .btn-ticket:hover, nav .btn-ticket:focus {
    background-color: #a5e700;
    color: #000;
    border: solid 2px #a5e700;
}

.forpc{
  display: block;
}
.forphone{
  display: none;
}

.mbr-parallax-background-fixed{
    position: relative;
    background-attachment: fixed;
    background-position: 50% top;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-color: #000;
    background-image: url(../img/spbg.jpg);
}

/*第一屏*/
.section-top{
    height: 100vh;
    overflow: hidden;
}
#main{
    position: relative;
    background-position: left top;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-color: #020921;
    background-image: url(../img/spmain2.jpg);
    color: #fff;
}
#main:after{
    content: "";
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, #00000069 10%, #000000cc 80%);
    backdrop-filter: blur(8px);
    position: absolute;
    top:0;
    left: 0;
}
#main.flex {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
#main .summit {
    max-width: 200px;
}
#main .main-sub{
    z-index: 5;
}
.main-title{
    font-size: 4.2rem;
    line-height: 1.05;
    font-weight: 800;
    letter-spacing: 2px;
}
.main-title2{
    font-size: 3rem;
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: 2px;
}
.main-txt {
    margin-top: 3rem;
}
.main-txt h6{
    font-weight: 300;
}
.main-txt a{
    margin-top: .5rem;
    color: #000;
    font-weight: bold;
    font-size: 1rem;
}
.content-title01 {
    font-size: 2.2rem;
    font-weight: 600;
    margin: 0;
}
.content-titlesm {
    font-size: 1.2rem;
    line-height: 1.5;
    font-weight: 400;
    margin-bottom: 30px;
    font-family: 'Candal', 'Oswald',sans-serif;
}

#num {
   background-image:linear-gradient(320deg, #55a6ff 20%, #082366);
}
#num .box{
    color: #000000c3;
    margin-bottom: 2rem;
}
#num .box .main-num {
    font-size: 3rem;
    line-height: 1.2;
    font-weight: 800;
    color: #ccfd01;
}
#num .box .sub-num {
    font-size: 2rem;
    color: #ccfd01;
}
#num .box h4{
    color: #dddddd;
}

#why{
    color: #fff;
    background: rgba(0,0,0,.5);
}
#why .flex{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}
#why .box{
    flex-basis: 45%;
    overflow: hidden;
}
#why .box2{
    flex-basis: 55%;
    overflow: hidden;
}
#why .box2 ul li{
    list-style: none;
    margin: 0px 0 20px 0;
    padding-bottom: 20px;
}
#why .box2 ul .tag {
/*
    border: 1.5px solid #34b9f7;
    padding: 0.1em 0.3em;
*/
    padding-bottom: 10px;
    display: inline-block;
    font-weight: 700;
}
#why .box2 ul span {
    margin-right: 20px;
}

#logo {
    background-color: #fff;
    color: #000;
}
#logo .logowall {
    display: flex;
    overflow: hidden;
}
#logo .box img, #logo .box-sec img{
    max-width:250px;
    padding: 0 10px;
}
#logo .box{
    animation: 15s linear infinite scroll;
    display: flex;
    flex-shrink: 0;
}
#logo .box-sec{
    animation: 15s linear infinite scroll2;
    display: flex;
    flex-shrink: 0;
}
@keyframes scroll {
    form{
        transform: translateX(100%);
    }

    to {
        transform: translateX(-100%);
    }
}

@keyframes scroll2 {
    form{
        transform: translateX(100%);
    }

    to {
        transform: translateX(-100%);
    }
}


#partner{
    color: #fff;
    background: rgba(0,0,0,.5);
}
#partner table {
    position: relative;
    width: 100%;
    backdrop-filter: blur(10px);
}
#partner .table-switch tbody tr, #partner .table-phone tbody tr{
    border-bottom: 1px solid #ffffff78;
}
#partner .table-switch thead th, #partner .table-switch tbody th{
    padding: 0.8rem;
}
#partner .table-switch tbody td {
    padding: 0.8rem;
    vertical-align: top;
}
#partner .table-phone tbody td {
    padding: 0.8rem 4px;
    vertical-align: top;
}
#partner .table-switch tbody td p,#partner .table-switch tbody td h6{
    margin: 0;
}
#partner .program-0 {
    background-color: #438f9640;
    line-height: 1.2;
}
#partner .program-2 {
    background-color: #00fffa;
    color: #000;
    line-height: 1.2;
}
#partner .program-1 {
    background-color: #feff00;
    color: #000;
    line-height: 1.2;
}
#partner .program-3 {
    background-color: #f0f0f0;
    color: #000;
    line-height: 1.2;
}
#partner .program-4 {
    background-color: #fc6c40;
    color: #000;
    line-height: 1.2;
}
#partner .program-2b {
    background-color: #00fffa;
    color: #000;
}
#partner .program-1b {
    background-color: #feff00;
    color: #000;
}
#partner .program-3b {
    background-color: #f0f0f0;
    color: #000;
}
#partner .program-4b {
    background-color: #fc6c40;
    color: #000;
}

#partner .table-switch tbody tr .w-line, #partner .table-phone .w-line{
    border-top: 2px solid #000;
}

#partner table .list td{
    padding: 0.5rem 0.8rem;
    background-color: #676767;
    color: #ffffff80;
}
#partner table tr.list, #partner table tr.noline{
    border-bottom: none;
}
#partner table td.h6, #partner .table-phone td.p{
    font-weight: 500;
}
#partner .table-switch td.p, #partner .table-phone td.txt{
    font-weight: 300;
}
#partner .table-phone td.txt{
    font-size: .85rem;
    background-color: #e0e0e03d;
}
#partner .sp-txt{
    font-weight: 800;
    transform: rotate(-4deg) scale(1.15);
    background: linear-gradient(90deg, #FFEB3B 10%, #d9932a 100%);;
}

#partner .table-phone {
    display: none;
}
/* #partner .table-switch .program-2::after{
    content: "限量4組";
    font-size: 1.2rem;
    font-weight: 600;
    background: linear-gradient(90deg, #34b9f7 10%, #172a88 100%);
    color: #fff;
    position: absolute;
    top: -20px;
    left: 15px;
    transform: rotate(5deg);
    padding: 2px 4px;
    animation: 1s linear infinite upup;
}
#partner .table-phone .program-2::after{
    content: "限量4組";
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(90deg, #34b9f7 10%, #172a88 100%);
    color: #fff;
    position: absolute;
    top: -20px;
    left: 20px;
    transform: rotate(5deg);
    padding: 2px 4px;
    animation: 1.1s linear infinite upup;
} */
@keyframes upup {
    0%{
        top: -20px;
    }
    40% {
        top: -25px;
    }
    60% {
        top: -25px;
    }
    100%{
        top: -20px;
    }
}
#partner .table-switch .program-1::after{
    content: "熱門選擇";
    font-size: 1.2rem;
    font-weight: 600;
    background: linear-gradient(90deg, #FFEB3B 10%, #d9932a 100%);
    color: #795548;
    position: absolute;
    top: -20px;
    left: 15px;
    transform: rotate(-5deg);
    padding: 2px 4px;
    animation: 1s linear infinite upup;
}
#partner .table-phone .program-1::after{
    content: "熱門選擇";
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(90deg, #FFEB3B 10%, #d9932a 100%);
    color: #795548;
    position: absolute;
    top: -20px;
    left: 20px;
    transform: rotate(-5deg);
    padding: 2px 4px;
    animation: 1.1s linear infinite upup;
}


#forms {
    background-image:linear-gradient(120deg, #f96b40 20%, #ffc773);
}
#forms iframe{
    width:70%;
    display: flex;
    margin: auto;
}

#footer {
    background: #000;
}
#footer .con {
    margin-bottom: .5rem;
}
.adbtn {
    background-color: rgba(0, 0, 0, 0.7);
    padding: 8px;
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10;
    width: 100%;
}

/* ==================================================*/

@media (max-width: 992px) {
    .navbar-nav {
        flex-direction: column;
    }
    .nav-item {
        width: 100%;
        text-align: left;
    }
    .dropdown-menu {
        background-color: #959595d1;
    }
    .mainlogo img {
       max-width: 110px;
    }
    .mainlogo img.school {
        max-width: 200px;
    }
    #forms iframe{
        width:100%;
    }
    #partner .table-switch .program-1::after{
        left: 0px;
    }
}

@media (max-width: 768px) {
    .mbr-parallax-background-fixed {
        background-image: url(../img/spbg-sm.jpg);
        background-position: -99999px -99999px;
    }
    .mbr-parallax-background-fixed:before {
        content: "";
        position: fixed;
        z-index: -1;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        height: 100vh;
        background-image: url(../img/spbg-sm.jpg);
        -webkit-background-size: cover !important;
        -o-background-size: cover;
        background-size: cover !important;
        background-position: 50% top;
    }
    .content-title01 {
        font-size: 2.5rem;
    }
    .adbtn {
        display: block;
    }
    .main-title {
        font-size: 4.25rem;
    }
    .main-title2 {
        font-size: 1.25rem;
        margin-bottom: .5rem;
    }
    #why .flex {
        justify-content: center;
    }
    #why .box{
        flex-basis: 100%;
        margin-bottom: 1rem;
    }
    #why .box2{
        flex-basis: 100%;
    }
    #why .box2 ul{
        padding: 0;
        margin: 0;
    }
    #partner .table-phone {
        display: block;
    }
    #partner .table-switch {
        display: none;
    }
}

@media (max-width: 576px) {
    .mainlogo img {
        width: 90px;
        height: auto;
    }
    .forpc{
        display: none;
    }
    .forphone{
        display:block;
    }
    .content-title01 {
        font-size: 2rem;
    }
    #partner .table-phone .program-1::after, #partner .table-phone .program-2::after{
        left: 0px;
        padding: 0px 5px;
    }
}

@media (max-width: 414px) {
    #main .summit {
        max-width: 160px;
    }
    .main-sub h3 {
        font-size: 1.2rem;
    }
    .main-title {
        font-size: 3.5rem;
    }
    .main-title2 {
        font-size: 1.8rem;
    }
    .table-theme {
        font-size: 24px;
    }
    #num .box{
        flex-basis: 100%;
    }
}

@media (max-width: 375px) {
    .content-titlesm {
        font-size: 1rem;
    }
    .content-title01 {
        font-size: 1.6rem;
        letter-spacing: 0.1rem;
    }
    #partner .table-phone .program-1::after, #partner .table-phone .program-2::after{
        font-size: .9rem;
        padding: 2px;
    }
    #partner .table-phone .date-en.h6 {
        font-size: .6rem;
    }
}
.line-through {
    text-decoration: line-through;
    font-size: 80%;
    opacity: 0.7;
}

/* Add these gradient text styles for pricing */
.sp-txt1, .sp-txt2, .sp-txt3 {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 800;
    display: inline-block;
    letter-spacing: -1px;
    transform: scale(1.15);
    font-size: 1.7rem;
}

@media (max-width: 768px) {
    .sp-txt1, .sp-txt2, .sp-txt3 {
        font-size: 1rem;
    }
}

.sp-txt1 {
    background-image: linear-gradient(45deg, #ffd700, #ff8c00);
}

.sp-txt2 {
    background-image: linear-gradient(45deg, #00fffa, #0066ff);
}

.sp-txt3 {
    background-image: linear-gradient(45deg, #f0f0f0, #a0a0a0);
}
