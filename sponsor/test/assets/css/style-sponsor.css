@import url("https://fonts.googleapis.com/css2?family=Alata&display=swap");
body {
    font-family: "Noto Sans TC", "微軟正黑體", <PERSON><PERSON>, "<PERSON><PERSON><PERSON>","Noto Sans TC", sans-serif;
    font-style: normal;
    line-height: 1.5;
    font-size: 16px;
    color: #fff;
}
.mbr-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
}
.mbr-section-hidden {
    overflow: hidden;
}
.date-en {
   font-family: 'Candal', 'Oswald',sans-serif;
}

a {
  color: #ff8b00;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
a:hover {
  text-decoration: none !important;
}
.btn-primary {
  background-color: #ff2871;
  color: #fff;
  border: solid 2px #ff2871;
}
.btn-primary:hover,.btn-primary:focus {
  background-color: #d0054a;
  color: #fff;
  border: solid 2px #d0054a;
}
.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  background-color: #d0054a;
  color: #fff;
  border: solid 2px #d0054a;
}
.card {
  position: relative;
  padding: 0;
  border: 0;
  border-radius: 0;
  margin: 0;
  overflow: hidden;
}

.navbar {
  margin: auto;
  width: 100%;
  background: #89a3abc3;
  padding-left: 30px;
  padding-right: 30px;
}
.navbar-fixed-top {
  position: fixed;
  top: 0;
  z-index: 15;
}
.mainlogo {
  display: flex;
  align-items: center;
}
.mainlogo img {
  max-width: 140px;
  text-align: left;
  padding-right: 5px;
}
.mainlogo img.school {
  max-width: 260px;
}
.navbar-expand-lg .navbar-collapse {
  flex-basis: 100%;
}
.nav-item {
  flex-wrap: wrap;
  position: relative;
  margin-left: 12px;
}
.nav-item a {
    color: var(--white-color);
    font-size: 1rem;
    line-height: 1rem;
    letter-spacing: 1px;
}
.navbar-collapse {
  justify-content: flex-end;
}
.navbar-nav {
  display: flex;
  flex-direction: row;
  text-align: center;
  justify-content: center;
  align-items: center;
  margin: 0;
}
.navbar .btn{
  padding: 0.2rem 0.8rem 0.4rem;
  margin: 0px;
  color: #000;
  font-weight: bold;
  font-size: 1rem;
  margin: 0 0.2rem;
}
.dropdown:hover .dropdown-menu {
    display: block;
}
a.dropdown-item:focus, a.dropdown-item:hover {
    text-decoration: none;
    background-color: #34b9f7;
}
.dropdown-item.item-close {
    color: #ccc;
}

.nav-item a.btn-ticket, .btn-ticket {
    background-color: #ccff00;
    color: #000;
    border: solid 2px #ccff00;
    border-radius: 2rem;
    padding: 0.75rem 1rem !important;
}
.nav-item a.btn-ticket:hover, .nav-item a.btn-ticket:focus, .btn-ticket:hover, nav .btn-ticket:focus {
    background-color: #a5e700;
    color: #000;
    border: solid 2px #a5e700;
}

.forpc{
  display: block;
}
.forphone{
  display: none;
}

.mbr-parallax-background-fixed{
    position: relative;
    background-attachment: fixed;
    background-position: 50% top;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-color: #000;
    background-image: url(../img/spbg.jpg);
}

/*第一屏*/
.section-top{
    height: 100vh;
}
#main{
    position: relative;
    background-position: left top;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-color: #020921;
    background-image: url(../img/spmain2.jpg);
    color: #fff;
}
#main:after{
    content: "";
    width: 100%;
    height: 100%;
    background-color: #020921b0;
    position: absolute;
    top:0;
    left: 0;
}
#main.flex {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
#main .summit {
    max-width: 200px;
}
#main .main-sub{
    z-index: 5;
}
.main-title{
    font-size: 4.2rem;
    line-height: 1.25;
    font-weight: 700;
    letter-spacing: 2px;
}
.main-title2{
    font-size: 3rem;
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: 2px;
}
.main-txt {
    margin-top: 4rem;
}
.main-txt h6{
    font-weight: 300;
}
.main-txt a{
    margin-top: .5rem;
    color: #000;
    font-weight: bold;
    font-size: 1rem;
}
.content-title01 {
    font-size: 2.2rem;
    font-weight: 600;
    margin: 0;
}
.content-titlesm {
    font-size: 1.2rem;
    line-height: 1.5;
    font-weight: 400;
    margin-bottom: 30px;
    font-family: 'Candal', 'Oswald',sans-serif;
}

#num {
    background: #323C89;
/*    background-image: linear-gradient(-65deg, #100E2C, #323C89);*/
}
#num .flex{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
#num .flex .box{
    flex-basis: 25%;
}
#num .flex .box .main-num {
    font-size: 60px;
    line-height: 1.2;
    font-weight: 600;
    color: #34b9f7;
}
#num .flex .box .sub-num {
    font-size: 30px;
    color: #34b9f7;
}

#why{
    color: #fff;
    background: rgba(0,0,0,.5);
}
#why .flex{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}
#why .box{
    flex-basis: 45%;
    overflow: hidden;
}
#why .box2{
    flex-basis: 55%;
    overflow: hidden;
}
#why .box2 ul li{
    list-style: none;
    margin: 0px 0 20px 0;
    padding-bottom: 20px;
}
#why .box2 ul .tag {
/*
    border: 1.5px solid #34b9f7;
    padding: 0.1em 0.3em;
*/
    padding-bottom: 10px;
    display: inline-block;
    font-weight: 700;
}
#why .box2 ul span {
    margin-right: 20px;
}

#logo {
    background-color: #fff;
    color: #000;
}
#logo .logowall {
    display: flex;
    overflow: hidden;
}
#logo .box img, #logo .box-sec img{
    max-width:250px;
    padding: 0 10px;
}
#logo .box{
    animation: 15s linear infinite scroll;
    display: flex;
    flex-shrink: 0;
}
#logo .box-sec{
    animation: 15s linear infinite scroll2;
    display: flex;
    flex-shrink: 0;
}
@keyframes scroll {
    form{
        transform: translateX(100%);
    }

    to {
        transform: translateX(-100%);
    }
}

@keyframes scroll2 {
    form{
        transform: translateX(100%);
    }

    to {
        transform: translateX(-100%);
    }
}


#partner{
    color: #fff;
    background: rgba(0,0,0,.5);
}
#partner table {
    position: relative;
    width: 100%;
}
#partner .table-switch tbody tr, #partner .table-phone tbody tr{
    border-bottom: 1px solid #ffffff78;
}
#partner .table-switch thead th, #partner .table-switch tbody th{
    padding: 0.8rem;
}
#partner .table-switch tbody td {
    padding: 0.8rem;
    vertical-align: top;
}
#partner .table-phone tbody td {
    padding: 0.8rem 0.5rem;
    vertical-align: top;
}
#partner .table-switch tbody td p,#partner .table-switch tbody td h6{
    margin: 0;
}
#partner .program-1 {
    background-color: #00fffa;
    color: #000;
    line-height: 1.2;
}
#partner .program-2 {
    background-color: #feff00;
    color: #000;
    line-height: 1.2;
}
#partner .program-3 {
    background-color: #f0f0f0;
    color: #000;
    line-height: 1.2;
}
#partner .program-4 {
    background-color: #fc6c40;
    color: #000;
    line-height: 1.2;
}
#partner .program-1b {
    background-color: #00fffa;
    color: #000;
}
#partner .program-2b {
    background-color: #feff00;
    color: #000;
}
#partner .program-3b {
    background-color: #f0f0f0;
    color: #000;
}
#partner .program-4b {
    background-color: #fc6c40;
    color: #000;
}

#partner .table-switch tbody tr .w-line, #partner .table-phone .w-line{
    border-top: 2px solid #000;
}

#partner table .list td{
    padding: 0.5rem 0.8rem;
    background-color: #676767;
    color: #ffffff80;
}
#partner table tr.list, #partner table tr.noline{
    border-bottom: none;
}
#partner table td.h6, #partner .table-phone td.p{
    font-weight: 700;
}
#partner .table-switch td.p, #partner .table-phone td.txt{
    font-weight: 300;
}
#partner .table-phone td.txt{
    font-size: .85rem;
    background-color: #e0e0e03d;
}

#partner .table-phone {
    display: none;
}
#partner .table-switch .program-1::after{
    content: "限量4組";
    font-size: 1.2rem;
    font-weight: 600;
    background: linear-gradient(90deg, #34b9f7 10%, #172a88 100%);
    color: #fff;
    position: absolute;
    top: -20px;
    left: 15px;
    transform: rotate(5deg);
    padding: 2px 4px;
    animation: 1s linear infinite upup;
}
#partner .table-phone .program-1::after{
    content: "限量4組";
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(90deg, #34b9f7 10%, #172a88 100%);
    color: #fff;
    position: absolute;
    top: -20px;
    left: 20px;
    transform: rotate(5deg);
    padding: 2px 4px;
    animation: 1.1s linear infinite upup;
}
@keyframes upup {
    0%{
        top: -20px;
    }
    40% {
        top: -25px;
    }
    60% {
        top: -25px;
    }
    100%{
        top: -20px;
    }
}
#partner .table-switch .program-2::after{
    content: "熱門選擇";
    font-size: 1.2rem;
    font-weight: 600;
    background: linear-gradient(90deg, #FFEB3B 10%, #d9932a 100%);
    color: #795548;
    position: absolute;
    top: -20px;
    left: 15px;
    transform: rotate(5deg);
    padding: 2px 4px;
    animation: 1s linear infinite upup;
}
#partner .table-phone .program-2::after{
    content: "熱門選擇";
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(90deg, #FFEB3B 10%, #d9932a 100%);
    color: #795548;
    position: absolute;
    top: -20px;
    left: 20px;
    transform: rotate(5deg);
    padding: 2px 4px;
    animation: 1.1s linear infinite upup;
}


#forms {
    background: #34b9f7;
}
#forms iframe{
    width:70%;
    display: flex;
    margin: auto;
}

#footer {
    background: #000;
}
#footer .con {
    margin-bottom: .5rem;
}
.adbtn {
    background-color: rgba(0, 0, 0, 0.7);
    padding: 8px;
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10;
    width: 100%;
}

/* ==================================================*/

@media (max-width: 992px) {
    .navbar-nav {
        flex-direction: column;
    }
    .nav-item {
        width: 100%;
        text-align: left;
    }
    .dropdown-menu {
        background-color: #ffffffd1;
    }
    .mainlogo img {
       max-width: 110px;
    }
    .mainlogo img.school {
        max-width: 200px;
    }
    #forms iframe{
        width:100%;
    }
    #partner .table-switch .program-1::after{
        left: 0px;
    }
}

@media (max-width: 768px) {
    .mbr-parallax-background-fixed {
        background-image: url(../img/spbg-sm.jpg);
        background-position: -99999px -99999px;
    }
    .mbr-parallax-background-fixed:before {
        content: "";
        position: fixed;
        z-index: -1;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        height: 100vh;
        background-image: url(../img/spbg-sm.jpg);
        -webkit-background-size: cover;!important;
        -o-background-size: cover;
        background-size: cover;!important;
        background-position: 50% top;
    }
    .content-title01 {
        font-size: 2.5rem;
    }
    .adbtn {
        display: block;
    }
    .main-title {
        font-size: 4rem;
    }
    .main-title2 {
        font-size: 2.5rem;
    }
    #num .flex .box{
        flex-basis: 50%;
        margin-bottom: 1rem;
    }
    #why .flex {
        justify-content: center;
    }
    #why .box{
        flex-basis: 100%;
        margin-bottom: 1rem;
    }
    #why .box2{
        flex-basis: 100%;
    }
    #why .box2 ul{
        padding: 0;
        margin: 0;
    }
    #partner .table-phone {
        display: block;
    }
    #partner .table-switch {
        display: none;
    }
}

@media (max-width: 576px) {
    .mainlogo img {
        width: 90px;
        height: auto;
    }
    .forpc{
        display: none;
    }
    .forphone{
        display:block;
    }
    .content-title01 {
        font-size: 2rem;
    }
    #partner .table-phone .program-1::after, #partner .table-phone .program-2::after{
        left: 0px;
        padding: 0px 5px;
    }
}

@media (max-width: 414px) {
    #main .summit {
        max-width: 160px;
    }
    .main-sub h3 {
        font-size: 1.2rem;
    }
    .main-title {
        font-size: 3.5rem;
    }
    .main-title2 {
        font-size: 1.8rem;
    }
    .table-theme {
        font-size: 24px;
    }
    #num .flex .box{
        flex-basis: 100%;
    }
}

@media (max-width: 375px) {
    .content-titlesm {
        font-size: 1rem;
    }
    .content-title01 {
        font-size: 1.6rem;
        letter-spacing: 0.1rem;
    }
    #partner .table-phone .program-1::after, #partner .table-phone .program-2::after{
        font-size: .9rem;
        padding: 2px;
    }
    #partner .table-phone .date-en.h6 {
        font-size: .6rem;
    }
}